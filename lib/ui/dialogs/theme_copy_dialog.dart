import 'package:flutter/material.dart';

import '../../models/copy_options.dart';
import '../../ui/AppTheme.dart';

/// 占星主題複製對話框
class ThemeCopyDialog extends StatefulWidget {
  const ThemeCopyDialog({super.key});

  @override
  State<ThemeCopyDialog> createState() => _ThemeCopyDialogState();
}

class _ThemeCopyDialogState extends State<ThemeCopyDialog> {
  String? _selectedTheme;

  // 定義各種占星主題及其所需的星盤資訊
  final Map<String, ThemeInfo> _themes = {
    'relationship': const ThemeInfo(
      title: '感情關係',
      subtitle: '現況、分合、未來發展',
      description: '分析感情關係的現況、可能的分合時機，以及未來發展趨勢',
      icon: Icons.favorite,
      color: Colors.pink,
      copyOptions: CopyOptions(
        includeBasicInfo: true,
        includePlanetPositions: true,
        includePlanetDignities: true,
        includePlanetSectStatus: false,
        includeHouseRulers: true,
        includeHousePositions: true,
        includeAspects: true,
        includeReceptions: true,
        includeElementStats: true,
        includeArabicPoints: true,
        usePrettyFormat: false,
      ),
      keyPoints: [
        '第7宮（伴侶宮）及其宮主星',
        '金星（愛情）與火星（激情）位置',
        '月亮（情感需求）與太陽（自我表達）',
        '第5宮（戀愛）與第8宮（深度關係）',
        '相位關係（特別是金星、火星、月亮）',
        '互容接納關係',
        '愛情相關阿拉伯點',
      ],
    ),
    'career': const ThemeInfo(
      title: '職涯方向與轉職評估',
      subtitle: '事業發展、轉職時機',
      description: '評估職業方向、事業發展潛力，以及轉職的最佳時機',
      icon: Icons.work,
      color: Colors.blue,
      copyOptions: CopyOptions(
        includeBasicInfo: true,
        includePlanetPositions: true,
        includePlanetDignities: true,
        includePlanetSectStatus: true,
        includeHouseRulers: true,
        includeHousePositions: true,
        includeAspects: true,
        includeReceptions: true,
        includeElementStats: true,
        includeArabicPoints: true,
        usePrettyFormat: true,
      ),
      keyPoints: [
        '第10宮（事業宮）及其宮主星',
        '第6宮（工作）與第2宮（收入）',
        '太陽（領導力）與土星（責任）',
        '水星（溝通）與木星（擴展）',
        '中天星座與相位',
        '日夜盤行星力量分析',
        '事業相關阿拉伯點',
      ],
    ),
    'self_exploration': const ThemeInfo(
      title: '自我探索、內在瓶頸',
      subtitle: '性格分析、成長方向',
      description: '深入了解個人性格特質、內在潛能與成長瓶頸',
      icon: Icons.psychology,
      color: Colors.purple,
      copyOptions: CopyOptions(
        includeBasicInfo: true,
        includePlanetPositions: true,
        includePlanetDignities: true,
        includePlanetSectStatus: true,
        includeHouseRulers: true,
        includeHousePositions: true,
        includeAspects: true,
        includeReceptions: true,
        includeElementStats: true,
        includeArabicPoints: false,
        usePrettyFormat: true,
      ),
      keyPoints: [
        '太陽（核心自我）與上升點（外在表現）',
        '月亮（內在需求）與情感模式',
        '第1宮（自我）與第12宮（潛意識）',
        '困難相位與挑戰',
        '元素分布與性格傾向',
        '行星尊貴力量與天賦',
        '日夜盤特質分析',
      ],
    ),
    'annual_fortune': const ThemeInfo(
      title: '流年運勢與重要時機建議',
      subtitle: '年度運勢、關鍵時機',
      description: '分析流年運勢變化，提供重要決策的最佳時機建議',
      icon: Icons.timeline,
      color: Colors.orange,
      copyOptions: CopyOptions(
        includeBasicInfo: true,
        includePlanetPositions: true,
        includePlanetDignities: true,
        includePlanetSectStatus: true,
        includeHouseRulers: true,
        includeHousePositions: true,
        includeAspects: true,
        includeReceptions: true,
        includeElementStats: false,
        includeArabicPoints: true,
        usePrettyFormat: true,
      ),
      keyPoints: [
        '本命盤基礎結構',
        '行星運行週期與相位',
        '重要宮位與宮主星',
        '流年觸發點',
        '時機選擇相關阿拉伯點',
        '行星尊貴力量變化',
        '日夜盤時間能量',
      ],
    ),
    'wealth': const ThemeInfo(
      title: '財富能量與資源運用',
      subtitle: '財運分析、投資方向',
      description: '分析財富累積能力、投資理財方向與資源運用策略',
      icon: Icons.monetization_on,
      color: Colors.green,
      copyOptions: CopyOptions(
        includeBasicInfo: true,
        includePlanetPositions: true,
        includePlanetDignities: true,
        includePlanetSectStatus: true,
        includeHouseRulers: true,
        includeHousePositions: true,
        includeAspects: true,
        includeReceptions: true,
        includeElementStats: true,
        includeArabicPoints: true,
        usePrettyFormat: true,
      ),
      keyPoints: [
        '第2宮（個人財富）與第8宮（他人資源）',
        '第11宮（收益）與宮主星',
        '金星（價值觀）與木星（擴展）',
        '土星（儲蓄）與冥王星（投資）',
        '財富相關相位關係',
        '資源運用能力分析',
        '財富相關阿拉伯點',
      ],
    ),
    'health': const ThemeInfo(
      title: '健康與身體狀況',
      subtitle: '體質分析、疾病預防',
      description: '分析個人體質特徵、健康傾向與疾病預防方向',
      icon: Icons.health_and_safety,
      color: Colors.red,
      copyOptions: CopyOptions(
        includeBasicInfo: true,
        includePlanetPositions: true,
        includePlanetDignities: true,
        includePlanetSectStatus: true,
        includeHouseRulers: true,
        includeHousePositions: true,
        includeAspects: true,
        includeReceptions: true,
        includeElementStats: true,
        includeArabicPoints: true,
        usePrettyFormat: true,
      ),
      keyPoints: [
        '第6宮（健康）與第12宮（慢性疾病）',
        '第1宮（體質）與上升點',
        '太陽（生命力）與火星（體力）',
        '月亮（消化系統）與土星（骨骼）',
        '困難相位與健康挑戰',
        '元素分布與體質傾向',
        '健康相關阿拉伯點',
      ],
    ),
    'education': const ThemeInfo(
      title: '學習與教育發展',
      subtitle: '學習能力、教育方向',
      description: '分析學習天賦、教育發展方向與知識領域選擇',
      icon: Icons.school,
      color: Colors.indigo,
      copyOptions: CopyOptions(
        includeBasicInfo: true,
        includePlanetPositions: true,
        includePlanetDignities: true,
        includePlanetSectStatus: false,
        includeHouseRulers: true,
        includeHousePositions: true,
        includeAspects: true,
        includeReceptions: true,
        includeElementStats: true,
        includeArabicPoints: false,
        usePrettyFormat: true,
      ),
      keyPoints: [
        '第3宮（基礎學習）與第9宮（高等教育）',
        '水星（思維）與木星（智慧）',
        '第5宮（創意學習）與宮主星',
        '土星（專業深度）與天王星（創新思維）',
        '學習相關相位關係',
        '智力發展模式分析',
        '適合的學習領域',
      ],
    ),
    'family': const ThemeInfo(
      title: '家庭與親子關係',
      subtitle: '家庭關係、親子互動',
      description: '分析家庭關係模式、親子互動與家庭責任',
      icon: Icons.family_restroom,
      color: Colors.brown,
      copyOptions: CopyOptions(
        includeBasicInfo: true,
        includePlanetPositions: true,
        includePlanetDignities: true,
        includePlanetSectStatus: true,
        includeHouseRulers: true,
        includeHousePositions: true,
        includeAspects: true,
        includeReceptions: true,
        includeElementStats: true,
        includeArabicPoints: false,
        usePrettyFormat: true,
      ),
      keyPoints: [
        '第4宮（家庭根基）與第10宮（家庭地位）',
        '第5宮（子女）與第11宮（家庭希望）',
        '月亮（母親）與土星（父親）',
        '太陽（家庭角色）與金星（家庭和諧）',
        '家庭相關相位關係',
        '親子互動模式分析',
        '家庭責任與傳承',
      ],
    ),
    'social': const ThemeInfo(
      title: '人際關係與社交',
      subtitle: '社交能力、友誼模式',
      description: '分析社交能力、友誼建立模式與人際網絡發展',
      icon: Icons.people,
      color: Colors.cyan,
      copyOptions: CopyOptions(
        includeBasicInfo: true,
        includePlanetPositions: true,
        includePlanetDignities: true,
        includePlanetSectStatus: false,
        includeHouseRulers: true,
        includeHousePositions: true,
        includeAspects: true,
        includeReceptions: true,
        includeElementStats: true,
        includeArabicPoints: false,
        usePrettyFormat: true,
      ),
      keyPoints: [
        '第11宮（友誼）與第7宮（合作關係）',
        '第3宮（溝通）與宮主星',
        '金星（社交魅力）與水星（溝通技巧）',
        '木星（人緣）與天秤座相關行星',
        '社交相關相位關係',
        '群體互動模式分析',
        '人際網絡建立能力',
      ],
    ),
    'creativity': const ThemeInfo(
      title: '創意與藝術天賦',
      subtitle: '創作能力、藝術表達',
      description: '分析創意潛能、藝術天賦與創作表達方式',
      icon: Icons.palette,
      color: Colors.deepPurple,
      copyOptions: CopyOptions(
        includeBasicInfo: true,
        includePlanetPositions: true,
        includePlanetDignities: true,
        includePlanetSectStatus: false,
        includeHouseRulers: true,
        includeHousePositions: true,
        includeAspects: true,
        includeReceptions: true,
        includeElementStats: true,
        includeArabicPoints: false,
        usePrettyFormat: true,
      ),
      keyPoints: [
        '第5宮（創意）與第12宮（靈感）',
        '金星（美感）與海王星（想像力）',
        '月亮（直覺）與天王星（創新）',
        '太陽（創作自我）與水星（表達技巧）',
        '創意相關相位關係',
        '藝術天賦分析',
        '創作表達方式',
      ],
    ),
    'spirituality': const ThemeInfo(
      title: '靈性成長與修行',
      subtitle: '靈性傾向、修行方向',
      description: '分析靈性發展傾向、修行方向與精神追求',
      icon: Icons.self_improvement,
      color: Colors.amber,
      copyOptions: CopyOptions(
        includeBasicInfo: true,
        includePlanetPositions: true,
        includePlanetDignities: true,
        includePlanetSectStatus: true,
        includeHouseRulers: true,
        includeHousePositions: true,
        includeAspects: true,
        includeReceptions: true,
        includeElementStats: true,
        includeArabicPoints: true,
        usePrettyFormat: true,
      ),
      keyPoints: [
        '第9宮（哲學）與第12宮（靈性）',
        '海王星（靈性）與冥王星（轉化）',
        '木星（智慧）與土星（修行紀律）',
        '月亮交點（業力方向）',
        '靈性相關相位關係',
        '修行方式分析',
        '靈性成長阿拉伯點',
      ],
    ),
    'location': const ThemeInfo(
      title: '居住與環境選擇',
      subtitle: '居住環境、地理影響',
      description: '分析適合的居住環境、地理位置對個人的影響',
      icon: Icons.location_on,
      color: Colors.teal,
      copyOptions: CopyOptions(
        includeBasicInfo: true,
        includePlanetPositions: true,
        includePlanetDignities: true,
        includePlanetSectStatus: false,
        includeHouseRulers: true,
        includeHousePositions: true,
        includeAspects: true,
        includeReceptions: false,
        includeElementStats: true,
        includeArabicPoints: false,
        usePrettyFormat: true,
      ),
      keyPoints: [
        '第4宮（居住根基）與第10宮（社會環境）',
        '第9宮（遠方）與第3宮（近距離移動）',
        '月亮（居住舒適度）與土星（穩定性）',
        '天王星（變遷）與海王星（理想環境）',
        '居住相關相位關係',
        '環境適應能力分析',
        '地理位置選擇建議',
      ],
    ),
    'legal': const ThemeInfo(
      title: '法律與合約事務',
      subtitle: '法律事務、合約簽署',
      description: '分析法律事務處理能力、合約簽署與糾紛處理',
      icon: Icons.gavel,
      color: Colors.blueGrey,
      copyOptions: CopyOptions(
        includeBasicInfo: true,
        includePlanetPositions: true,
        includePlanetDignities: true,
        includePlanetSectStatus: true,
        includeHouseRulers: true,
        includeHousePositions: true,
        includeAspects: true,
        includeReceptions: true,
        includeElementStats: false,
        includeArabicPoints: true,
        usePrettyFormat: true,
      ),
      keyPoints: [
        '第7宮（合約）與第9宮（法律）',
        '第8宮（糾紛）與第12宮（隱藏問題）',
        '土星（規則）與木星（正義）',
        '水星（文件）與冥王星（權力鬥爭）',
        '法律相關相位關係',
        '合約處理能力分析',
        '法律相關阿拉伯點',
      ],
    ),
  };

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.royalIndigo.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.content_copy,
              color: AppColors.royalIndigo,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          const Expanded(
            child: Text(
              '選擇占星主題',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              '請選擇您要分析的占星主題，系統將自動複製相關的星盤資訊：',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
            const SizedBox(height: 16),
            Flexible(
              child: SingleChildScrollView(
                child: Column(
                  children: _themes.entries.map((entry) {
                    final themeKey = entry.key;
                    final themeInfo = entry.value;
                    final isSelected = _selectedTheme == themeKey;

                    return Container(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(12),
                          onTap: () {
                            setState(() {
                              _selectedTheme = themeKey;
                            });
                          },
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: isSelected
                                    ? themeInfo.color
                                    : Colors.grey.withValues(alpha: 0.3),
                                width: isSelected ? 2 : 1,
                              ),
                              color: isSelected
                                  ? themeInfo.color.withValues(alpha: 0.1)
                                  : Colors.transparent,
                            ),
                            child: Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: themeInfo.color.withValues(alpha: 0.2),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Icon(
                                    themeInfo.icon,
                                    color: themeInfo.color,
                                    size: 20,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        themeInfo.title,
                                        style: TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.bold,
                                          color: isSelected
                                              ? themeInfo.color
                                              : Colors.black87,
                                        ),
                                      ),
                                      Text(
                                        themeInfo.subtitle,
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.grey[600],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                if (isSelected)
                                  Icon(
                                    Icons.check_circle,
                                    color: themeInfo.color,
                                    size: 20,
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
        ElevatedButton(
          onPressed: _selectedTheme != null
              ? () {
                  final selectedThemeInfo = _themes[_selectedTheme!]!;
                  Navigator.of(context).pop({
                    'theme': _selectedTheme,
                    'themeInfo': selectedThemeInfo,
                    'copyOptions': selectedThemeInfo.copyOptions,
                  });
                }
              : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.royalIndigo,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
          child: const Text('複製星盤資訊'),
        ),
      ],
    );
  }
}

/// 主題資訊類別
class ThemeInfo {
  final String title;
  final String subtitle;
  final String description;
  final IconData icon;
  final Color color;
  final CopyOptions copyOptions;
  final List<String> keyPoints;

  const ThemeInfo({
    required this.title,
    required this.subtitle,
    required this.description,
    required this.icon,
    required this.color,
    required this.copyOptions,
    required this.keyPoints,
  });
}

/// 顯示主題複製對話框
Future<Map<String, dynamic>?> showThemeCopyDialog(BuildContext context) async {
  return showDialog<Map<String, dynamic>>(
    context: context,
    builder: (context) => const ThemeCopyDialog(),
  );
}
