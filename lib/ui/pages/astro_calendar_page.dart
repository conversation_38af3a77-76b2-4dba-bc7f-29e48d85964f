import 'package:flutter/material.dart';
import 'package:flutter_datetime_picker_plus/flutter_datetime_picker_plus.dart'
    as date_picker;
import 'package:provider/provider.dart';
import 'package:table_calendar/table_calendar.dart';

import '../../models/astro_event.dart';
import '../../ui/AppTheme.dart';
import '../../viewmodels/astro_calendar_viewmodel.dart';
import '../../widgets/astro_event_marker.dart';
import 'astro_event_detail_page.dart';

/// 星象日曆頁面
class AstroCalendarPage extends StatefulWidget {
  const AstroCalendarPage({super.key});

  @override
  State<AstroCalendarPage> createState() => _AstroCalendarPageState();
}

class _AstroCalendarPageState extends State<AstroCalendarPage> {
  late AstroCalendarViewModel _viewModel;

  @override
  void initState() {
    super.initState();
    _viewModel = AstroCalendarViewModel();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeAfterPageLoad();
    });
  }

  /// 頁面完全載入後初始化
  void _initializeAfterPageLoad() async {
    if (mounted) {
      try {
        await _viewModel.initialize();
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('載入星象日曆失敗: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  void dispose() {
    _viewModel.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _viewModel,
      child: Consumer<AstroCalendarViewModel>(
        builder: (context, viewModel, child) {
          return Scaffold(
            appBar: _buildAppBar(viewModel),
            body: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppColors.scaffoldBackground,
                    AppColors.scaffoldBackground.withValues(alpha: 0.8),
                  ],
                ),
              ),
              child: Stack(
                children: [
                  Column(
                    children: [
                      // 頂部統計信息條
                      _buildStatsBar(viewModel),

                      // 日曆組件
                      _buildCalendar(viewModel),

                      // 選中日期的事件詳情
                      Expanded(
                        child: _buildEventDetails(viewModel),
                      ),
                    ],
                  ),
                  if (viewModel.isLoading)
                    const Center(child: CircularProgressIndicator()),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// 構建日曆組件
  Widget _buildCalendar(AstroCalendarViewModel viewModel) {
    return Container(
      margin: const EdgeInsets.fromLTRB(12, 8, 12, 8),
      padding: const EdgeInsets.only(bottom: 8, top: 0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.royalIndigo.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: AppColors.royalIndigo.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: TableCalendar<AstroEvent>(
          key: ValueKey(viewModel.selectedEventTypes.hashCode),
          firstDay: DateTime.utc(1800, 1, 1),
          lastDay: DateTime.utc(3000, 12, 31),
          focusedDay: viewModel.focusedDay,
          selectedDayPredicate: (day) => isSameDay(viewModel.selectedDay, day),
          calendarFormat: viewModel.calendarFormat,
          eventLoader: viewModel.getEventsForDay,
          startingDayOfWeek: StartingDayOfWeek.monday,

          // 優化的樣式設定
          calendarStyle: CalendarStyle(
            outsideDaysVisible: false,
            weekendTextStyle: TextStyle(
              color: AppColors.error.withValues(alpha: 0.8),
              fontWeight: FontWeight.w500,
            ),
            holidayTextStyle: TextStyle(
              color: AppColors.error.withValues(alpha: 0.8),
              fontWeight: FontWeight.w500,
            ),
            selectedDecoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.royalIndigo,
                  AppColors.royalIndigo.withValues(alpha: 0.8),
                ],
              ),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: AppColors.royalIndigo.withValues(alpha: 0.3),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            todayDecoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.solarAmber,
                  AppColors.solarAmber.withValues(alpha: 0.8),
                ],
              ),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: AppColors.solarAmber.withValues(alpha: 0.3),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            markerDecoration: const BoxDecoration(
              color: AppColors.royalIndigo,
              shape: BoxShape.circle,
            ),
            cellMargin: const EdgeInsets.all(4),
            defaultTextStyle: const TextStyle(
              fontWeight: FontWeight.w500,
            ),
          ),

          // 優化的標題樣式
          headerStyle: HeaderStyle(
            formatButtonVisible: true,
            titleCentered: true,
            formatButtonShowsNext: false,
            formatButtonDecoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.royalIndigo,
                  AppColors.royalIndigo.withValues(alpha: 0.8),
                ],
              ),
              borderRadius: const BorderRadius.all(Radius.circular(12.0)),
              boxShadow: [
                BoxShadow(
                  color: AppColors.royalIndigo.withValues(alpha: 0.2),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            formatButtonTextStyle: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
            leftChevronIcon: Icon(
              Icons.chevron_left,
              color: AppColors.royalIndigo.withValues(alpha: 0.7),
            ),
            rightChevronIcon: Icon(
              Icons.chevron_right,
              color: AppColors.royalIndigo.withValues(alpha: 0.7),
            ),
          ),

          // 自定義標題構建器
          calendarBuilders: CalendarBuilders<AstroEvent>(
            // 自定義標題
            headerTitleBuilder: (context, day) {
              return GestureDetector(
                onTap: () => _showDatePicker(viewModel),
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppColors.royalIndigo.withValues(alpha: 0.1),
                        AppColors.solarAmber.withValues(alpha: 0.05),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: AppColors.royalIndigo.withValues(alpha: 0.3),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.royalIndigo.withValues(alpha: 0.1),
                        blurRadius: 2,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: IntrinsicWidth(
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.calendar_month,
                          color: AppColors.royalIndigo.withValues(alpha: 0.7),
                          size: 14,
                        ),
                        const SizedBox(width: 4),
                        Flexible(
                          child: Text(
                            '${day.year}年${day.month}月',
                            style: const TextStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.bold,
                              color: AppColors.royalIndigo,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        const SizedBox(width: 2),
                        Icon(
                          Icons.arrow_drop_down,
                          color: AppColors.royalIndigo.withValues(alpha: 0.7),
                          size: 16,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },

            // 自定義事件標記
            markerBuilder: (context, day, events) {
              if (events.isNotEmpty) {
                return Positioned(
                  bottom: 2,
                  child: AstroEventMarker(
                    events: events,
                    size: 7.0,
                    showCount: true,
                  ),
                );
              }
              return null;
            },

            // 自定義日期單元格
            defaultBuilder: (context, day, focusedDay) {
              return Container(
                margin: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.transparent,
                ),
                child: Center(
                  child: Text(
                    '${day.day}',
                    style: const TextStyle(
                      color: AppColors.textDark,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              );
            },
          ),

          // 事件回調
          onDaySelected: (selectedDay, focusedDay) {
            viewModel.setSelectedDay(selectedDay, autoLoad: true);
            viewModel.setFocusedDay(focusedDay, autoLoad: false);
          },

          onFormatChanged: (format) {
            viewModel.setCalendarFormat(format);
          },

          onPageChanged: (focusedDay) async {
            // 切換月份時載入該月份第一天的星象
            final firstDayOfMonth = DateTime(focusedDay.year, focusedDay.month, 1);
            viewModel.setFocusedDay(focusedDay, autoLoad: false);
            viewModel.setSelectedDay(firstDayOfMonth, autoLoad: true);
          },
        ),
      ),
    );
  }

  /// 構建事件詳情
  Widget _buildEventDetails(AstroCalendarViewModel viewModel) {
    return Container(
      margin: const EdgeInsets.fromLTRB(12, 0, 12, 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.royalIndigo.withValues(alpha: 0.08),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
        border: Border.all(
          color: AppColors.royalIndigo.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Column(
          children: [
            // 事件詳情標題欄
            _buildEventDetailsHeader(viewModel),

            // 事件列表
            Expanded(
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                child: DailyEventDetail(
                  selectedDate: viewModel.selectedDay,
                  events: viewModel.selectedDayEvents,
                  onTapEvent: (event) =>
                      _navigateToEventDetail(event, viewModel),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建事件詳情標題欄
  Widget _buildEventDetailsHeader(AstroCalendarViewModel viewModel) {
    final selectedDate = viewModel.selectedDay;
    final eventsCount = viewModel.selectedDayEvents.length;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.royalIndigo.withValues(alpha: 0.05),
            AppColors.solarAmber.withValues(alpha: 0.02),
          ],
        ),
        border: Border(
          bottom: BorderSide(
            color: AppColors.royalIndigo.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.royalIndigo.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.event_note,
              color: AppColors.royalIndigo,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${selectedDate.year}年${selectedDate.month}月${selectedDate.day}日',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.royalIndigo,
                  ),
                ),
                Text(
                  eventsCount > 0 ? '$eventsCount 個星象事件' : '無特殊星象事件',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          if (eventsCount > 0)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: AppColors.solarAmber.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '$eventsCount',
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: AppColors.solarAmber,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 顯示日期選擇器
  void _showDatePicker(AstroCalendarViewModel viewModel) {
    date_picker.DatePicker.showDatePicker(
      context,
      showTitleActions: true,
      minTime: DateTime(1800, 1, 1),
      maxTime: DateTime(3000, 12, 31),
      onConfirm: (selectedDate) async {
        // 確定按鈕的回調 - 載入選中日期的星象
        try {
          // 設置新的日期並載入該日期的星象事件
          viewModel.setFocusedDay(selectedDate, autoLoad: false);
          viewModel.setSelectedDay(selectedDate, autoLoad: false);

          // 載入選中日期的星象事件
          await viewModel.loadDayEvents(selectedDate);
        } catch (e) {
          // 錯誤處理
          viewModel.setLoading(false);
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('載入星象事件失敗: $e'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      },
      onCancel: () {
        // 取消按鈕的回調（可選）
        // 用戶取消了日期選擇
      },
      currentTime: viewModel.focusedDay,
      locale: date_picker.LocaleType.tw,
      theme: const date_picker.DatePickerTheme(
        backgroundColor: Colors.white,
        headerColor: Colors.white,
        itemStyle: TextStyle(
          color: Colors.black87,
          fontSize: 18,
          fontWeight: FontWeight.w500,
        ),
        doneStyle: TextStyle(
          color: AppColors.royalIndigo,
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
        cancelStyle: TextStyle(
          color: Colors.grey,
          fontSize: 16,
        ),
        titleHeight: 50.0,
        containerHeight: 240.0,
        itemHeight: 40.0,
      ),
    );
  }

  /// 顯示篩選對話框
  void _showFilterDialog() {
    // 在對話框外部獲取 viewModel 引用，避免 Provider 作用域問題
    final viewModel = _viewModel;
    showDialog(
      context: context,
      builder: (dialogContext) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Row(
            children: [
              const Icon(Icons.filter_list, color: AppColors.royalIndigo),
              const SizedBox(width: 8),
              const Text('事件篩選'),
              const Spacer(),
              Text(
                '${viewModel.selectedEventTypes.length}/${AstroEventType.values.length}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          content: SizedBox(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                EventTypeFilter(
                  selectedTypes: viewModel.selectedEventTypes,
                  onToggle: (type) {
                    viewModel.toggleEventType(type);
                    setState(() {}); // 更新對話框狀態
                  },
                  getDisplayName: viewModel.getEventTypeDisplayName,
                  getIcon: viewModel.getEventTypeIcon,
                  getColor: viewModel.getEventTypeColor,
                ),
                const SizedBox(height: 16),
                // 添加全選/全不選按鈕
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    TextButton.icon(
                      onPressed: () {
                        // 全選
                        for (final type in AstroEventType.values) {
                          if (!viewModel.selectedEventTypes.contains(type)) {
                            viewModel.toggleEventType(type);
                          }
                        }
                        setState(() {}); // 更新對話框狀態
                      },
                      icon: const Icon(Icons.select_all, size: 16),
                      label: const Text('全選'),
                    ),
                    TextButton.icon(
                      onPressed: () {
                        // 全不選
                        final typesToRemove = List<AstroEventType>.from(
                            viewModel.selectedEventTypes);
                        for (final type in typesToRemove) {
                          viewModel.toggleEventType(type);
                        }
                        setState(() {}); // 更新對話框狀態
                      },
                      icon: const Icon(Icons.clear_all, size: 16),
                      label: const Text('全不選'),
                    ),
                  ],
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: const Text('關閉'),
            ),
          ],
        ),
      ),
    );
  }

  /// 導航到事件詳情頁面
  void _navigateToEventDetail(
      AstroEvent event, AstroCalendarViewModel viewModel) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AstroEventDetailPage(
          event: event,
          natalPerson: viewModel.natalPerson,
          latitude: viewModel.latitude,
          longitude: viewModel.longitude,
        ),
      ),
    );
  }

  /// 構建AppBar
  PreferredSizeWidget _buildAppBar(AstroCalendarViewModel viewModel) {
    return AppBar(
      title: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: AppColors.solarAmber.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.calendar_month,
              color: AppColors.solarAmber,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          const Text(
            '星象日曆',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              letterSpacing: 0.5,
            ),
          ),
        ],
      ),
      backgroundColor: AppColors.royalIndigo,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        // 篩選按鈕
        Container(
          margin: const EdgeInsets.only(right: 4),
          child: IconButton(
            icon: Stack(
              children: [
                const Icon(Icons.filter_list),
                if (viewModel.selectedEventTypes.length <
                    AstroEventType.values.length)
                  Positioned(
                    right: 0,
                    top: 0,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: const BoxDecoration(
                        color: AppColors.solarAmber,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
              ],
            ),
            onPressed: _showFilterDialog,
            tooltip: '事件篩選',
          ),
        ),
        // 刷新按鈕
        Container(
          margin: const EdgeInsets.only(right: 8),
          child: IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => viewModel.refresh(),
            tooltip: '重新載入',
          ),
        ),
      ],
    );
  }

  /// 構建統計信息條
  Widget _buildStatsBar(AstroCalendarViewModel viewModel) {
    final selectedDayEvents = viewModel.selectedDayEvents;
    final monthEvents =
        viewModel.events.values.expand((events) => events).length;

    return Container(
      margin: const EdgeInsets.fromLTRB(12, 8, 12, 0),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.royalIndigo.withValues(alpha: 0.1),
            AppColors.solarAmber.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.royalIndigo.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // 當日事件數
          Expanded(
            child: _buildStatItem(
              icon: Icons.today,
              label: '今日事件',
              value: '${selectedDayEvents.length}',
              color: AppColors.royalIndigo,
            ),
          ),
          Container(
            width: 1,
            height: 24,
            color: AppColors.royalIndigo.withValues(alpha: 0.2),
          ),
          // 本月事件數
          Expanded(
            child: _buildStatItem(
              icon: Icons.calendar_month,
              label: '本月事件',
              value: '$monthEvents',
              color: AppColors.solarAmber,
            ),
          ),
          Container(
            width: 1,
            height: 24,
            color: AppColors.royalIndigo.withValues(alpha: 0.2),
          ),
          // 篩選狀態
          Expanded(
            child: _buildStatItem(
              icon: Icons.filter_list,
              label: '篩選類型',
              value:
                  '${viewModel.selectedEventTypes.length}/${AstroEventType.values.length}',
              color: AppColors.indigoLight,
            ),
          ),
        ],
      ),
    );
  }

  /// 構建統計項目
  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          color: color,
          size: 14,
        ),
        const SizedBox(height: 3),
        Text(
          value,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: color,
          ),
          overflow: TextOverflow.ellipsis,
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 9,
            color: Colors.grey[600],
          ),
          overflow: TextOverflow.ellipsis,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
